# Development Environment Configuration for ISMS Application
# This file contains environment variables for local development

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

# Vite Configuration
NODE_ENV=development
VITE_API_BASE_URL=http://localhost/api/v1
VITE_APP_NAME="ISMS Development"
VITE_APP_VERSION=0.1.0

# Development Features
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_DEBUG_LOGS=true

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================

# Application Mode
ENV_MODE=development

# Database Configuration
DATABASE_URL=sqlite:///./data/isms.db

# Security Configuration
SECRET_KEY=dev-secret-key-change-in-production-please
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration (JSON array as string)
BACKEND_CORS_ORIGINS='["http://localhost", "http://localhost:3000", "http://localhost:80", "http://127.0.0.1:3000"]'

# Rate Limiting (Relaxed for development)
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME="ISMS API"
DESCRIPTION="Inventory and Sales Management System API"
VERSION=0.1.0

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

# Container Names
COMPOSE_PROJECT_NAME=isms-dev

# Port Mappings
FRONTEND_PORT=3000
BACKEND_PORT=8000
PROXY_PORT=80
STATS_PORT=8404
HMR_PORT=3001
DEBUG_PORT=5678

# =============================================================================
# DEVELOPMENT TOOLS
# =============================================================================

# File Watching (for Docker on Windows/Mac)
CHOKIDAR_USEPOLLING=true
WATCHPACK_POLLING=true

# Hot Module Replacement
FAST_REFRESH=true

# =============================================================================
# OPTIONAL SERVICES
# =============================================================================

# PostgreSQL (if enabled)
POSTGRES_DB=isms
POSTGRES_USER=isms_user
POSTGRES_PASSWORD=isms_password
POSTGRES_HOST=database
POSTGRES_PORT=5432

# Redis (if enabled for caching)
REDIS_URL=redis://redis:6379/0

# =============================================================================
# DEBUGGING
# =============================================================================

# Python Debugger
PYTHONPATH=/app
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# Enable debug mode
DEBUG=true
