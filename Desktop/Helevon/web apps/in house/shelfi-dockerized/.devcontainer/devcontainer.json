{"name": "ISMS Development Environment", "dockerComposeFile": "../docker-compose.codespaces.yml", "service": "frontend", "workspaceFolder": "/app", "shutdownAction": "stopCompose", "features": {"ghcr.io/devcontainers/features/docker-in-docker:2": {}, "ghcr.io/devcontainers/features/node:1": {"version": "20"}, "ghcr.io/devcontainers/features/python:1": {"version": "3.12"}}, "forwardPorts": [3000, 8000, 80, 8404], "portsAttributes": {"3000": {"label": "Frontend (React)", "onAutoForward": "notify"}, "8000": {"label": "Backend (FastAPI)", "onAutoForward": "silent"}, "80": {"label": "HAProxy (Main App)", "onAutoForward": "silent"}, "8404": {"label": "HAProxy Stats", "onAutoForward": "silent"}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-python.python", "ms-python.flake8", "ms-python.black-formatter", "ms-vscode.vscode-json"], "settings": {"typescript.preferences.importModuleSpecifier": "relative", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "python.defaultInterpreterPath": "/usr/local/bin/python", "python.formatting.provider": "black"}}}, "postCreateCommand": "npm install", "postStartCommand": "bash scripts/codespaces-start.sh", "remoteUser": "node"}