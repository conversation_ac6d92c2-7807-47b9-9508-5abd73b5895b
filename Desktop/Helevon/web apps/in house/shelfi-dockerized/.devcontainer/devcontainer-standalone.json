{"name": "ISMS Standalone Development Environment", "image": "mcr.microsoft.com/devcontainers/javascript-node:20-bullseye", "workspaceFolder": "/workspaces/${localWorkspaceFolderBasename}", "features": {"ghcr.io/devcontainers/features/python:1": {"version": "3.12"}}, "forwardPorts": [3000, 8000], "portsAttributes": {"3000": {"label": "Frontend (React)", "onAutoForward": "notify"}, "8000": {"label": "Backend (FastAPI)", "onAutoForward": "silent"}}, "customizations": {"vscode": {"extensions": ["ms-vscode.vscode-typescript-next", "bradlc.vscode-tailwindcss", "esbenp.prettier-vscode", "ms-python.python", "ms-python.flake8", "ms-python.black-formatter", "ms-vscode.vscode-json"], "settings": {"typescript.preferences.importModuleSpecifier": "relative", "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "python.defaultInterpreterPath": "/usr/local/bin/python", "python.formatting.provider": "black"}}}, "postCreateCommand": "bash scripts/codespaces-standalone-setup.sh", "remoteUser": "vscode"}