global
    # HAProxy global configuration for ISMS development environment
    daemon
    log stdout local0 info
    chroot /var/lib/haproxy
    stats socket /run/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy

    # Default SSL material locations
    ca-base /etc/ssl/certs
    crt-base /etc/ssl/private

    # Intermediate configuration
    ssl-default-bind-ciphers ECDHE+AESGCM:ECDHE+CHACHA20:RSA+AESGCM:RSA+AES:!aNULL:!MD5:!DSS
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option log-health-checks
    option forwardfor except *********/8
    option redispatch
    retries 3
    timeout connect 5000
    timeout client 50000
    timeout server 50000
    errorfile 400 /etc/haproxy/errors/400.http
    errorfile 403 /etc/haproxy/errors/403.http
    errorfile 408 /etc/haproxy/errors/408.http
    errorfile 500 /etc/haproxy/errors/500.http
    errorfile 502 /etc/haproxy/errors/502.http
    errorfile 503 /etc/haproxy/errors/503.http
    errorfile 504 /etc/haproxy/errors/504.http

# Frontend for main application
frontend isms_frontend
    bind *:80
    
    # Security headers
    http-response set-header X-Frame-Options SAMEORIGIN
    http-response set-header X-Content-Type-Options nosniff
    http-response set-header X-XSS-Protection "1; mode=block"
    
    # Health check endpoint
    acl health_check path_beg /health
    http-request return status 200 content-type text/plain string "HAProxy healthy\n" if health_check
    
    # API routing - route /api requests to backend
    acl is_api path_beg /api/
    use_backend isms_backend if is_api
    
    # WebSocket support for Vite HMR (development)
    acl is_websocket hdr(Upgrade) -i websocket
    use_backend isms_frontend_ws if is_websocket
    
    # Default to frontend for all other requests
    default_backend isms_frontend_web

# Backend pool for API requests
backend isms_backend
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    
    # Backend server
    server backend1 backend:8000 check inter 10s fall 3 rise 2
    
    # CORS headers for API requests
    http-response set-header Access-Control-Allow-Origin "*"
    http-response set-header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    http-response set-header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Frontend web server pool
backend isms_frontend_web
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    
    # Frontend server
    server frontend1 frontend:3000 check inter 10s fall 3 rise 2

# Frontend WebSocket pool for Vite HMR (development only)
backend isms_frontend_ws
    balance roundrobin
    
    # Frontend WebSocket server for HMR
    server frontend_ws1 frontend:3001 check inter 10s fall 3 rise 2

# Statistics page
listen stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    stats auth admin:admin123
