global
    # HAProxy global configuration for ISMS development environment
    daemon
    log stdout local0 info
    stats socket /var/lib/haproxy/admin.sock mode 660 level admin
    stats timeout 30s
    user haproxy
    group haproxy

    # Default SSL material locations
    ca-base /etc/ssl/certs
    crt-base /etc/ssl/private

    # Intermediate configuration
    ssl-default-bind-ciphers ECDHE+AESGCM:ECDHE+CHACHA20:RSA+AESGCM:RSA+AES:!aNULL:!MD5:!DSS
    ssl-default-bind-options ssl-min-ver TLSv1.2 no-tls-tickets

defaults
    mode http
    log global
    option httplog
    option dontlognull
    option log-health-checks
    option forwardfor except *********/8
    option redispatch
    retries 3
    timeout connect 5000
    timeout client 50000
    timeout server 50000

# Frontend for main application
frontend isms_frontend
    bind *:80
    
    # Security headers
    http-response set-header X-Frame-Options SAMEORIGIN
    http-response set-header X-Content-Type-Options nosniff
    http-response set-header X-XSS-Protection "1; mode=block"
    
    # Health check endpoint for HAProxy itself
    acl haproxy_health_check path_beg /haproxy-health
    http-request return status 200 content-type text/plain string "HAProxy healthy\n" if haproxy_health_check
    
    # API routing - route /api requests to backend
    acl is_api path_beg /api/
    acl is_health path_beg /health
    acl is_docs path_beg /docs
    acl is_redoc path_beg /redoc
    use_backend isms_backend if is_api
    use_backend isms_backend if is_health
    use_backend isms_backend if is_docs
    use_backend isms_backend if is_redoc
    
    # WebSocket support for Vite HMR (development)
    acl is_websocket hdr(Upgrade) -i websocket
    use_backend isms_frontend_ws if is_websocket
    
    # Default to frontend for all other requests
    default_backend isms_frontend_web

# Backend pool for API requests
backend isms_backend
    balance roundrobin
    option httpchk GET /health
    http-check expect status 200
    
    # Backend server
    server backend1 backend:8000 check inter 10s fall 3 rise 2
    
    # CORS headers for API requests
    http-response set-header Access-Control-Allow-Origin "*"
    http-response set-header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS"
    http-response set-header Access-Control-Allow-Headers "Content-Type, Authorization, X-Requested-With"

# Frontend web server pool
backend isms_frontend_web
    balance roundrobin

    # Frontend server (no health check for Vite dev server)
    server frontend1 frontend:3000

# Frontend WebSocket pool for Vite HMR (development only)
backend isms_frontend_ws
    balance roundrobin

    # Frontend WebSocket server for HMR (no health check)
    server frontend_ws1 frontend:3001

# Statistics page
listen stats
    bind *:8404
    stats enable
    stats uri /stats
    stats refresh 30s
    stats admin if TRUE
    stats auth admin:admin123
