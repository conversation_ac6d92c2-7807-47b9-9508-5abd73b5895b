# Production Environment Configuration Template for ISMS Application
# Copy this file to .env.production and fill in the actual values

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================

NODE_ENV=production
VITE_API_BASE_URL=https://your-domain.com/api/v1
VITE_APP_NAME=ISMS
VITE_APP_VERSION=0.1.0

# Production Features (disabled for security)
VITE_ENABLE_DEVTOOLS=false
VITE_ENABLE_DEBUG_LOGS=false

# =============================================================================
# BACKEND CONFIGURATION
# =============================================================================

# Application Mode
ENV_MODE=production

# Database Configuration
DATABASE_URL=sqlite:///./data/isms.db
# For PostgreSQL: DATABASE_URL=postgresql://user:password@host:port/database

# Security Configuration (CHANGE THESE!)
SECRET_KEY=your-super-secret-key-here-change-this
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=1

# CORS Configuration
BACKEND_CORS_ORIGINS=["https://your-domain.com"]

# Rate Limiting (Strict for production)
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=ISMS API
DESCRIPTION=Inventory and Sales Management System API
VERSION=0.1.0

# =============================================================================
# DOCKER CONFIGURATION
# =============================================================================

COMPOSE_PROJECT_NAME=isms-prod

# =============================================================================
# SSL/TLS CONFIGURATION
# =============================================================================

# SSL Certificate paths (if using HTTPS)
SSL_CERT_PATH=/etc/ssl/certs/your-cert.pem
SSL_KEY_PATH=/etc/ssl/private/your-key.pem

# =============================================================================
# DATABASE CONFIGURATION (if using PostgreSQL)
# =============================================================================

POSTGRES_DB=isms_prod
POSTGRES_USER=isms_user
POSTGRES_PASSWORD=your-secure-password-here
POSTGRES_HOST=database
POSTGRES_PORT=5432

# =============================================================================
# MONITORING & LOGGING
# =============================================================================

# Sentry (Error Tracking)
SENTRY_DSN=your-sentry-dsn-here

# Log aggregation
LOG_AGGREGATION_ENDPOINT=your-log-endpoint-here

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================

# Database backup settings
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=/backups

# =============================================================================
# SECURITY HEADERS
# =============================================================================

# Content Security Policy
CSP_POLICY="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'"

# Additional security headers
HSTS_MAX_AGE=31536000
X_FRAME_OPTIONS=DENY
X_CONTENT_TYPE_OPTIONS=nosniff
