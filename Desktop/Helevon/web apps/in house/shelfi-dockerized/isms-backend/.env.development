# Backend Development Environment Variables

# Application Mode
ENV_MODE=development

# Database Configuration
DATABASE_URL=sqlite:///./data/isms.db

# Security Configuration
SECRET_KEY=dev-secret-key-change-in-production
ACCESS_TOKEN_EXPIRE_MINUTES=60
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
BACKEND_CORS_ORIGINS=["http://localhost", "http://localhost:3000", "http://localhost:80"]

# Rate Limiting (Relaxed for development)
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=60

# Logging Configuration
LOG_LEVEL=DEBUG
LOG_FORMAT=detailed

# API Configuration
API_V1_STR=/api/v1
PROJECT_NAME=ISMS API Development
DESCRIPTION=Inventory and Sales Management System API - Development
VERSION=0.1.0

# Python Configuration
PYTHONPATH=/app
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# Development Features
DEBUG=true
